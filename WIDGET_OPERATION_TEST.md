# Widget操作过滤功能测试指南

## 测试场景

### 1. 基本操作记录测试
**测试步骤：**
1. 在Widget上标记一张照片删除
2. 观察Widget是否切换到下一张照片
3. 等待Widget自动刷新
4. 验证被标记删除的照片不再显示

**预期结果：**
- 标记删除后立即切换到新照片
- 被标记的照片不再在Widget中显示
- 操作记录被正确保存

### 2. 切换操作记录测试
**测试步骤：**
1. 在Widget上点击切换按钮
2. 记录当前显示的照片
3. 多次点击切换按钮
4. 验证之前切换过的照片不会立即重复显示

**预期结果：**
- 每次切换都显示不同的照片
- 切换过的照片被记录为已操作
- 优先显示未操作过的照片

### 3. 所有照片操作完毕测试
**测试步骤：**
1. 对所有缓存的照片进行操作（删除或切换）
2. 观察Widget的显示状态
3. 尝试继续切换照片

**预期结果：**
- Widget显示"所有照片都已操作过，请点击打开APP更新缓存"
- 切换按钮不再显示新照片
- 点击Widget可以打开主应用

### 4. 操作记录清空测试
**测试步骤：**
1. 操作多张照片（标记删除）
2. 打开主应用，进入删除列表
3. 确认删除操作
4. 返回Widget查看显示状态

**预期结果：**
- 删除确认后操作记录被清空
- Widget重新显示所有可用照片
- 可以重新操作之前操作过的照片

### 5. 数据同步测试
**测试步骤：**
1. 在Widget上操作照片
2. 打开主应用
3. 检查操作记录是否同步
4. 关闭应用后重新打开

**预期结果：**
- 主应用启动时同步Widget操作记录
- 操作记录在应用重启后保持
- 数据在App Groups中正确共享

## 边界情况测试

### 1. 缓存照片不足
**场景：** 只有1-2张缓存照片
**测试：** 操作所有照片后的行为
**预期：** 正确显示提示信息

### 2. 网络异常
**场景：** 无网络连接时的操作
**测试：** 操作记录是否正常保存
**预期：** 本地操作不受网络影响

### 3. 应用权限
**场景：** 相册权限被撤销
**测试：** Widget的显示和操作
**预期：** 显示合适的权限提示

## 性能测试

### 1. 大量操作记录
**测试：** 操作100+张照片后的性能
**指标：** Widget刷新速度，内存使用

### 2. 频繁切换
**测试：** 快速连续点击切换按钮
**指标：** 响应速度，数据一致性

### 3. 长期使用
**测试：** 连续使用一周后的状态
**指标：** 数据完整性，存储空间

## 用户体验测试

### 1. 直观性测试
- 用户是否能理解操作记录的概念
- 提示信息是否清晰易懂
- 操作流程是否符合预期

### 2. 一致性测试
- 不同Widget尺寸的行为是否一致
- 主应用和Widget的数据是否同步
- 操作反馈是否及时

### 3. 错误恢复测试
- 意外情况下的数据恢复
- 操作记录损坏时的处理
- 缓存清空后的重建

## 测试工具

### 1. 日志检查
```bash
# 查看Widget日志
log show --predicate 'subsystem contains "MyWidgetExtension"' --last 1h

# 查看主应用日志
log show --predicate 'subsystem contains "photoclear"' --last 1h
```

### 2. 数据检查
```swift
// 检查操作记录
let userDefaults = UserDefaults(suiteName: "group.com.lfb.manager.photoclear.shared")
let operatedPhotos = userDefaults?.array(forKey: "operatedPhotos")
print("操作过的照片: \(operatedPhotos)")
```

### 3. 性能监控
- 使用Xcode Instruments监控内存使用
- 检查Widget刷新频率
- 监控文件I/O操作

## 回归测试清单

- [ ] 原有删除功能正常
- [ ] 原有切换功能正常  
- [ ] 原有点击跳转功能正常
- [ ] 新增操作记录功能正常
- [ ] 新增过滤逻辑正常
- [ ] 数据同步功能正常
- [ ] 边界情况处理正常
- [ ] 性能指标达标
- [ ] 用户体验良好