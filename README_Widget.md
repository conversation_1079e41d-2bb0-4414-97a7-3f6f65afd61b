# 相册照片Widget功能说明

## 功能概述
这个Widget可以在桌面上随机展示系统相册中的照片，每小时自动更新显示不同的照片。

## 实现原理

### 1. App Groups共享数据
- 主应用和Widget通过App Groups (`group.com.lfb.manager.photoclear.shared`) 共享数据
- 照片缓存在共享容器的 `WidgetPhotos` 目录中

### 2. 主应用功能
- **自动缓存**: 应用启动时自动缓存10张随机照片
- **定时更新**: 每小时检查一次，自动更新照片缓存
- **手动更新**: 提供"更新Widget照片"按钮，用户可手动触发更新
- **权限管理**: 自动请求相册访问权限

### 3. Widget功能
- **随机显示**: 每次更新时从缓存中随机选择一张照片显示
- **定时刷新**: 每小时自动更新一次
- **优雅降级**: 如果没有照片，显示默认渐变背景
- **信息显示**: 显示当前时间和缓存照片数量

## 使用方法

### 首次使用
1. 打开主应用，系统会自动请求相册访问权限
2. 授权后，应用会自动缓存照片
3. 添加Widget到桌面，即可看到随机照片

### 日常使用
- Widget会每小时自动更新显示不同照片
- 打开主应用可手动更新照片缓存
- 应用会在后台定期更新照片缓存

## 技术特点

### 性能优化
- 照片压缩为300x300像素，减少存储空间
- 使用JPEG格式，压缩率80%
- 限制缓存照片数量为10张

### 用户体验
- 支持小号和中号Widget尺寸
- 照片采用填充模式，自动裁剪适配
- 底部渐变遮罩，确保文字可读性
- 显示照片数量徽章

### 隐私保护
- 照片仅在本地缓存，不上传到服务器
- 使用系统相册权限，用户完全控制
- 缓存照片定期清理更新

## 文件结构

```
photoclear/
├── PhotoTools.h/m              # 照片处理工具类
├── ViewController.m            # 主界面，包含手动更新功能
├── AppDelegate.m              # 应用生命周期管理
└── Info.plist                 # 包含App Groups配置

MyWidgetExtension/
├── MyWidgetExtension.swift     # Widget主要逻辑
├── SharedPhotoManager.swift    # 共享照片管理类
├── MyWidgetExtensionBundle.swift # Widget Bundle
└── Info.plist                 # Widget配置
```

## 注意事项

1. **权限要求**: 需要相册访问权限
2. **存储空间**: 缓存照片约占用2-3MB空间
3. **更新频率**: Widget每小时更新一次，避免过度消耗电量
4. **兼容性**: 支持iOS 14.0及以上版本

## 故障排除

### Widget不显示照片
1. 检查相册权限是否已授权
2. 打开主应用，点击"更新Widget照片"
3. 确认相册中有照片

### Widget不更新
1. 重新添加Widget到桌面
2. 重启设备
3. 检查系统设置中Widget刷新权限