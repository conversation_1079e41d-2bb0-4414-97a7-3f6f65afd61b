//
//  ViewController.m
//  photoclear
//
//  Created by lifubing on 2021/6/15.
//

#import "BaseViewController.h"
#import "PhotoTools.h"
#import "photoclear-Swift.h"
#import "ScrollViewController.h"

@interface BaseViewController ()
@end

@implementation BaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor blackColor];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

- (void)reloadWidgetTimelines {
    // 使用运行时检查WidgetKit是否可用
    [WidgetKitHelper reloadAllWidgets]; // 刷新widget
}

#pragma mark - Widget Photo Selection

- (void)handleShowPhotoFromWidget:(NSNotification *)notification {
    NSString *photoIdentifier = notification.userInfo[@"photoIdentifier"];
    if (photoIdentifier && photoIdentifier.length > 0) {
        [self navigateToScrollViewControllerWithPhotoIdentifier:photoIdentifier];
    }
}

- (void)navigateToScrollViewControllerWithPhotoIdentifier:(NSString *)photoIdentifier {
    // 创建ScrollViewController并传递照片标识符
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    
    // 尝试从storyboard获取ScrollViewController
    ScrollViewController *scrollVC = nil;
    @try {
        scrollVC = [storyboard instantiateViewControllerWithIdentifier:@"ScrollViewController"];
    } @catch (NSException *exception) {
        // 如果storyboard中没有，直接创建
        scrollVC = [[ScrollViewController alloc] init];
    }
    
    if (scrollVC) {
        // 设置照片数据
        scrollVC.photoAssetArray = [PhotoTools allPhotos];
        
        // 将照片标识符保存到共享存储中，供ScrollViewController读取
        NSUserDefaults *defaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
        [defaults setObject:photoIdentifier forKey:@"selectedPhotoIdentifier"];
        [defaults synchronize];
        
        // 跳转到ScrollViewController
        [self.navigationController pushViewController:scrollVC animated:YES];
        
        NSLog(@"Navigating to ScrollViewController with photo identifier: %@", photoIdentifier);
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
