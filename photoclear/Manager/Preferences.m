//
//  Preferences.m
//  PAPreferencesSample
//
//  Created by <PERSON> on 29/04/2014.
//  Copyright (c) 2014 Peer Assembly. All rights reserved.
//

#import "Preferences.h"

@implementation Preferences

@dynamic deleteArray;
@dynamic archiveArray;
@dynamic lastLocalIdentifier;
@dynamic lastAlbumLocalIdentifier;
@dynamic autoPlayEnabled;
@dynamic autoPlayInterval;
@dynamic swipeDirection;

- (id)init {
    if (self = [super init]) {
        self.userDefaultsSuiteName = @"group.com.lfb.manager.photoclear.shared";
        
        // 设置默认值
        NSUserDefaults *defaults = [self userDefaults];
        if ([defaults objectForKey:@"autoPlayEnabled"] == nil) {
            self.autoPlayEnabled = NO;
        }
        if ([defaults objectForKey:@"autoPlayInterval"] == nil) {
            self.autoPlayInterval = 3; // 默认3秒
        }
        if ([defaults objectForKey:@"swipeDirection"] == nil) {
            self.swipeDirection = 0; // 默认上下滑动
        }
    }
    return self;
}

@end
