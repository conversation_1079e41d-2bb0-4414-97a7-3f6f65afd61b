//
//  NotificationManager.h
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/19.
//

#import <Foundation/Foundation.h>
#import <UserNotifications/UserNotifications.h>

NS_ASSUME_NONNULL_BEGIN

@interface NotificationManager : NSObject

+ (instancetype)sharedManager;

// 权限管理
- (void)requestNotificationPermission:(nullable void(^)(BOOL granted, NSError * _Nullable error))completion;
- (void)checkNotificationPermission:(void(^)(BOOL granted))completion;

// 发送通知
- (void)sendNotificationWithTitle:(NSString *)title body:(NSString *)body;

// 通知管理
- (void)removeAllPendingNotifications;
- (void)removeAllDeliveredNotifications;

@end

NS_ASSUME_NONNULL_END
