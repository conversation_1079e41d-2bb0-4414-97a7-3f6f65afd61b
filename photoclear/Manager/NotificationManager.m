//
//  NotificationManager.m
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/19.
//

#import "NotificationManager.h"

@interface NotificationManager ()
@property (nonatomic, strong) UNUserNotificationCenter *notificationCenter;
@end

@implementation NotificationManager

+ (instancetype)sharedManager {
    static NotificationManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[NotificationManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _notificationCenter = [UNUserNotificationCenter currentNotificationCenter];
    }
    return self;
}

#pragma mark - Permission Management

- (void)requestNotificationPermission:(nullable void(^)(BOOL granted, NSError * _Nullable error))completion {
    [self.notificationCenter requestAuthorizationWithOptions:(UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge)
                                           completionHandler:^(BOOL granted, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (granted) {
                NSLog(@"[NotificationManager] 通知权限已获得");
            } else {
                NSLog(@"[NotificationManager] 通知权限被拒绝: %@", error.localizedDescription);
            }
            
            if (completion) {
                completion(granted, error);
            }
        });
    }];
}

- (void)checkNotificationPermission:(void(^)(BOOL granted))completion {
    [self.notificationCenter getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings * _Nonnull settings) {
        BOOL granted = (settings.authorizationStatus == UNAuthorizationStatusAuthorized);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (completion) {
                completion(granted);
            }
        });
    }];
}

#pragma mark - Send Notifications

- (void)sendNotificationWithTitle:(NSString *)title body:(NSString *)body {
    // 先检查权限
    [self checkNotificationPermission:^(BOOL granted) {
        if (!granted) {
            NSLog(@"[NotificationManager] 没有通知权限，无法发送通知");
            return;
        }
        
        [self _sendNotificationWithTitle:title body:body];
    }];
}

- (void)_sendNotificationWithTitle:(NSString *)title body:(NSString *)body {
    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
    content.title = title;
    content.body = body;
    content.sound = [UNNotificationSound defaultSound];
    content.badge = @0;
    
    // 立即触发通知
    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:1 repeats:NO];
    
    NSString *identifier = [NSString stringWithFormat:@"PhotoClear_%@_%@", 
                           @((NSInteger)[[NSDate date] timeIntervalSince1970]), 
                           [[NSUUID UUID] UUIDString]];
    
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:identifier 
                                                                          content:content 
                                                                          trigger:trigger];
    
    [self.notificationCenter addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            NSLog(@"[NotificationManager] 发送通知失败: %@", error.localizedDescription);
        } else {
            NSLog(@"[NotificationManager] 通知已发送: %@ - %@", title, body);
        }
    }];
}

#pragma mark - Notification Management

- (void)removeAllPendingNotifications {
    [self.notificationCenter removeAllPendingNotificationRequests];
    NSLog(@"[NotificationManager] 已清除所有待发送通知");
}

- (void)removeAllDeliveredNotifications {
    [self.notificationCenter removeAllDeliveredNotifications];
    NSLog(@"[NotificationManager] 已清除所有已发送通知");
}

@end
