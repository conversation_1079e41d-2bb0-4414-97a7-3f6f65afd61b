//
//  PhotoTools.h
//  photoclear
//
//  Created by lifubing on 2021/6/29.
//

#import <Foundation/Foundation.h>
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface PhotoTools : NSObject

+ (PHFetchResult<PHAsset *> *)allPhotos;

+ (void)asyncCacheRandomPhotosForWidgetCompletion:(void(^)(BOOL success))completion; // 异步缓存

+ (void)normalCacheSmallRandomPhotosForWidgetCompletion:(void(^)(BOOL success))completion;

+ (NSURL *)sharedContainerURL;
+ (NSArray<NSString *> *)getCachedPhotoNames;
+ (void)syncDeleteArrayFromWidget;
+ (void)syncOperatedPhotosFromWidget;
+ (NSArray<NSString *> *)getOperatedPhotos;
+ (void)clearOperatedPhotos;

// 照片缓存次数管理
+ (NSDictionary<NSString *, NSNumber *> *)getPhotoCacheCount;
+ (void)incrementCacheCountForPhoto:(NSString *)localIdentifier;
+ (void)clearPhotoCacheCount;

@end

NS_ASSUME_NONNULL_END
