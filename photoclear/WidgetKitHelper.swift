//
//  WidgetKitHelper.swift
//  StepOfMyWorld
//
//  Created by lifubing on 2020/9/26.
//  Copyright © 2020 Li,Fubing. All rights reserved.
//

import WidgetKit
import ActivityKit

//@available(iOS 14.0, *)
@objcMembers final class WidgetKitHelper: NSObject {
    
    class func reloadAllWidgets(){
        #if arch(arm64) || arch(i386) || arch(x86_64)
        if #available(iOS 14, *) {
            NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(doReloadAllWidgets), object: nil)
            perform(#selector(doReloadAllWidgets), with: nil, afterDelay: 0.3)
        }
        #endif
    }
    
    @available(iOS 14.0, *)
    @objc private class func doReloadAllWidgets() {
        WidgetCenter.shared.reloadAllTimelines()
    }
        
}
