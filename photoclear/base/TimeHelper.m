//
//  TimeHelper.m
//  StepOfMyWorld
//
//  Created by <PERSON>,Fubing on 2018/4/30.
//  Copyright © 2018年 Li,Fubing. All rights reserved.
//

#import "TimeHelper.h"

@interface TimeHelper() {
    NSDateFormatter *formatter;
}

@end

@implementation TimeHelper

+ (TimeHelper *)shareInstance {
    static TimeHelper *manager = nil;
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        manager = [[TimeHelper alloc] init];
    });
    return manager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        formatter = [[NSDateFormatter alloc] init];
    }
    return self;
}

- (NSInteger)getHourByIntervalTime:(NSInteger)Time {
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
    // 这个时间是北京时间
    [formatter setDateFormat:@"HH"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSInteger)getMineOfDayByIntervalTime:(NSInteger)Time {
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
    // 这个时间是北京时间
    [formatter setDateFormat:@"mm"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSInteger)getSecondOfDayByIntervalTime:(NSInteger)Time {
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
    // 这个时间是北京时间
    [formatter setDateFormat:@"ss"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSInteger)getIntervalTimeWithStr:(NSString *)str {

//    2017-01-28T09:40:34.000+08:00
    
//    GGLog(@"%@",detectedDate);
//    __block NSDate *detectedDate;
//    NSDataDetector *detector = [NSDataDetector dataDetectorWithTypes:NSTextCheckingTypeDate error:nil];
//    NSLocale* currentLoc = [NSLocale currentLocale];
//    [detector enumerateMatchesInString:str
//                               options:kNilOptions
//                                 range:NSMakeRange(0, [str length])
//                            usingBlock:^(NSTextCheckingResult *result, NSMatchingFlags flags, BOOL *stop)
//     {
//         if ([result resultType] == NSTextCheckingTypeDate) {
//             GGLog(@"Date : %@", [[result date] descriptionWithLocale:currentLoc]);
//         }
//
//         detectedDate =
//         result.date;
//     }];
    
    formatter = [[NSDateFormatter alloc] init];
    [formatter setTimeZone:[NSTimeZone systemTimeZone]];
    [formatter setLocale:[NSLocale currentLocale]];
    [formatter setDateFormat:@"yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"];
    [formatter setFormatterBehavior:NSDateFormatterBehaviorDefault];

    NSDate *date = [formatter dateFromString:str];
    if (date == nil) {
        [formatter setDateFormat:@"yyyy-MM-dd'T'HH:mm:ssZ"];
        date = [formatter dateFromString:str];
    }
    NSInteger time = [date timeIntervalSince1970];
    return time;
}

- (NSInteger)getDayByIntervalTime:(NSInteger)Time {
    
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
//    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss SSS"];
    // 这个时间是北京时间
    [formatter setDateFormat:@"dd"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSInteger)getDayOfThisYearByIntervalTime:(NSInteger)Time {

    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
    // 这个时间是北京时间
    [formatter setDateFormat:@"D"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSInteger)getYearByIntervalTime:(NSInteger)Time {
    
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
    // 这个时间是北京时间
    [formatter setDateFormat:@"YYYY"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSInteger)getMonthByIntervalTime:(NSInteger)Time {
    
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
//    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss SSS"];
    // 这个时间是北京时间
    [formatter setDateFormat:@"MM"];
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat.integerValue;
}

- (NSString *)getDateByIntervalTime:(NSInteger)Time withformate:(NSString *)format {
    [formatter setDateStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的日期格式
    [formatter setTimeStyle:NSDateFormatterFullStyle];// 修改下面提到的北京时间的时间格式
    [formatter setDateFormat:format];
    // 这个时间是北京时间
    NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:Time];
    NSString *dat = [formatter stringFromDate:date1];
    return dat;
}

- (NSDate *)getZeroTimeWithTime:(NSInteger)time {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *now = [NSDate dateWithTimeIntervalSince1970:time];
    NSDateComponents *components = [calendar components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay fromDate:now];
    NSDate *startDate = [calendar dateFromComponents:components];
    return startDate;
}

- (NSInteger)getZeroDayIntervalTimeWithTime:(NSInteger)time {
    return [[self getZeroTimeWithTime:time] timeIntervalSince1970];
}

- (NSDate *)getZeroTime {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *now = [NSDate date];
    NSDateComponents *components = [calendar components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay fromDate:now];
    NSDate *startDate = [calendar dateFromComponents:components];
    return startDate;
}

- (NSDate *)getYearZeroTime {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDate *now = [NSDate date];
    NSDateComponents *components = [calendar components:NSCalendarUnitYear fromDate:now];
    NSDate *startDate = [calendar dateFromComponents:components];
    return startDate;
}

- (NSDate *)getZeroWeekTime {
    NSDate *nowDate = [NSDate date];
    NSCalendar *calendar = [NSCalendar currentCalendar];
    NSDateComponents *comp = [calendar components:NSYearCalendarUnit | NSMonthCalendarUnit | NSDayCalendarUnit | NSWeekdayCalendarUnit | NSDayCalendarUnit fromDate:nowDate];
    // 获取今天是周几
    NSInteger weekDay = [comp weekday];
    // 获取几天是几号
    NSInteger day = [comp day];
    // 计算当前日期和本周的星期一和星期天相差天数
    long firstDiff,lastDiff;
    if (weekDay == 1) {
        firstDiff = -6;
    } else {
        firstDiff = [calendar firstWeekday] - weekDay + 1;
    }
    // 在当前日期(去掉时分秒)基础上加上差的天数
    NSDateComponents *firstDayComp = [calendar components:NSYearCalendarUnit | NSMonthCalendarUnit | NSDayCalendarUnit  fromDate:nowDate];
    [firstDayComp setDay:day + firstDiff];
    NSDate *firstDayOfWeek = [calendar dateFromComponents:firstDayComp];
    
    return firstDayOfWeek;
}

- (NSDate *)getDateWithYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day{
    NSString *stringDate = [NSString stringWithFormat:@"%ld %ld, %ld",month,day,year];
    NSDateFormatter *dateFormat = [[NSDateFormatter alloc] init];
    [dateFormat setDateFormat:@"MM dd, yyyy"];
    NSDate *date = [dateFormat dateFromString:stringDate];
    return date;
}

#pragma mark --请求网络时间戳
+ (void)getInternetDateWithSuccess:(void(^)(NSTimeInterval timeInterval))success failure:(void(^)(NSError *error))failure{
    //1.创建URL
    NSString *urlString = @"http://m.baidu.com";
    urlString = [urlString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    //2.创建request请求对象
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    [request setURL:[NSURL URLWithString: urlString]];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setTimeoutInterval:5];
    [request setHTTPShouldHandleCookies:FALSE];
    [request setHTTPMethod:@"GET"];
    //3.创建URLSession对象
    NSURLSession *session = [NSURLSession sharedSession];
    //4.设置数据返回回调的block
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error == nil && response != nil) {
            //这么做的原因是简体中文下的手机不能识别“MMM”，只能识别“MM”
            NSArray *monthEnglishArray = @[@"Jan",@"Feb",@"Mar",@"Apr",@"May",@"Jun",@"Jul",@"Aug",@"Sept",@"Sep",@"Oct",@"Nov",@"Dec"];
            NSArray *monthNumArray = @[@"01",@"02",@"03",@"04",@"05",@"06",@"07",@"08",@"09",@"09",@"10",@"11",@"12"];
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            NSDictionary *allHeaderFields = [httpResponse allHeaderFields];
            NSString *dateStr = [allHeaderFields objectForKey:@"Date"];
            dateStr = [dateStr substringFromIndex:5];
            dateStr = [dateStr substringToIndex:[dateStr length]-4];
            dateStr = [dateStr stringByAppendingString:@" +0000"];
            //当前语言是中文的话，识别不了英文缩写
            for (NSInteger i = 0 ; i < monthEnglishArray.count ; i++) {
                NSString *monthEngStr = monthEnglishArray[i];
                NSString *monthNumStr = monthNumArray[i];
                dateStr = [dateStr stringByReplacingOccurrencesOfString:monthEngStr withString:monthNumStr];
                
            }
            NSDateFormatter *dMatter = [[NSDateFormatter alloc] init];
            [dMatter setDateFormat:@"dd MM yyyy HH:mm:ss"];
            NSDate *netDate = [dMatter dateFromString:dateStr];
            NSTimeInterval timeInterval = [netDate timeIntervalSince1970];
            dispatch_async(dispatch_get_main_queue(), ^{
                success(timeInterval);
                
            });
            
        }else{
            dispatch_async(dispatch_get_main_queue(), ^{
                failure(error);
                
                });
            
        }}];
    //5、执行网络请求
    [task resume];
    
}
@end


