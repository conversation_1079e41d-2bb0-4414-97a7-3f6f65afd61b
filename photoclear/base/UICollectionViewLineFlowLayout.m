//
//  UICollectionViewLineFlowLayout.m
//  photoclear
//
//  Created by lifubing on 2021/6/30.
//

#import "UICollectionViewLineFlowLayout.h"

#define screenWidth [UIScreen mainScreen].bounds.size.width
 
#define MaxChangeRange 100


#define ITEM_SIZE 200.0
#define ACTIVE_DISTANCE 200
#define ZOOM_FACTOR 0.4

@implementation UICollectionViewLineFlowLayout

//-(id)init
//{
//    self = [super init];
//    if (self) {
//        self.itemSize = CGSizeMake(ITEM_SIZE, ITEM_SIZE);
//        self.scrollDirection = UICollectionViewScrollDirectionHorizontal;
//        //  确定了缩进，此处为上方、下方各缩进200
//        self.sectionInset = UIEdgeInsetsMake(ITEM_SIZE, ITEM_SIZE/2, ITEM_SIZE, ITEM_SIZE/2);
//        //  每个item在水平方向的最小间距
//        self.minimumLineSpacing = ITEM_SIZE/4;
//
//    }
//    return self;
//}

- (void)prepareLayout {
    self.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    self.itemSize = CGSizeMake(300, 500);
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)oldBounds
{
    return YES;
}

- (nullable NSArray<__kindof UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
 NSArray *array = [super layoutAttributesForElementsInRect:rect];
 
 CGRect visibleRect = CGRectMake(self.collectionView.contentOffset.x, 0, self.collectionView.bounds.size.width, self.collectionView.bounds.size.height);
 for (UICollectionViewLayoutAttributes *attr in array)
 {
  if (CGRectIntersectsRect(attr.frame, rect)) {
 
   BOOL isAtRight = YES;
   CGFloat distance = (attr.center.x - CGRectGetMidX(visibleRect));
   if (distance<0) {
    distance = -distance;
    isAtRight = NO;
   }
   CGFloat precent ;
   if (distance < 180)
   {
    precent = 1.0;
   }
   else
   {
    precent = ((screenWidth / 2) - distance) / (screenWidth / 2);
   }
   CATransform3D transform = CATransform3DIdentity;
   transform.m34 = 1.0 / 600;
 
   if (precent < 0.5) {
    precent = 0.5;
   }
   transform = CATransform3DScale(transform, 1, precent, 1);
   CGFloat p = isAtRight?M_PI_4:-M_PI_4;
   transform = CATransform3DRotate(transform, p * (1 - precent), 0, 1, 0);
   attr.transform3D = transform;
   attr.zIndex = 1;
   attr.alpha = precent;
  }
 }
 
 return array;
}

- (CGPoint)targetContentOffsetForProposedContentOffset:(CGPoint)proposedContentOffset withScrollingVelocity:(CGPoint)velocity
{
 CGFloat offset = MAXFLOAT;
 
 CGFloat hCenter = proposedContentOffset.x + (CGRectGetWidth(self.collectionView.bounds) / 2.0);
 
 CGRect currentRect = CGRectMake(proposedContentOffset.x, 0, self.collectionView.bounds.size.width, self.collectionView.bounds.size.height);
 
 NSArray* array = [super layoutAttributesForElementsInRect:currentRect];
 for (UICollectionViewLayoutAttributes* layoutAttributes in array)
 {
  CGFloat itemHorizontalCenter = layoutAttributes.center.x;
  if (ABS(itemHorizontalCenter - hCenter) < ABS(offset))
  {
 
   offset = itemHorizontalCenter - hCenter;
  }
 }
 
 return CGPointMake(proposedContentOffset.x + offset, proposedContentOffset.y);
}

////  初始的layout外观将由该方法返回的UICollctionViewLayoutAttributes来决定
//-(NSArray*)layoutAttributesForElementsInRect:(CGRect)rect
//{
//    NSArray* array = [super layoutAttributesForElementsInRect:rect];
//    CGRect visibleRect;
//    visibleRect.origin = self.collectionView.contentOffset;
//    visibleRect.size = self.collectionView.bounds.size;
//    for (UICollectionViewLayoutAttributes* attributes in array) {
//        if (CGRectIntersectsRect(attributes.frame, rect)) {
//            CGFloat distance = CGRectGetMidX(visibleRect) - attributes.center.x;
//            CGFloat normalizedDistance = distance / ACTIVE_DISTANCE;
//            if (ABS(distance) < ACTIVE_DISTANCE) {
//                CGFloat zoom = 1 + ZOOM_FACTOR*(1 - ABS(normalizedDistance));
//                attributes.transform3D = CATransform3DMakeScale(zoom, zoom, 1.0);
//                attributes.zIndex = 1;
//            }
//        }
//    }
//    return array;
//}

////  自动对齐到网格
//- (CGPoint)targetContentOffsetForProposedContentOffset:(CGPoint)proposedContentOffset withScrollingVelocity:(CGPoint)velocity
//{
//    //  proposedContentOffset是没有对齐到网格时本来应该停下来的位置
//    CGFloat offsetAdjustment = MAXFLOAT;
//    CGFloat horizontalCenter = proposedContentOffset.x + (CGRectGetWidth(self.collectionView.bounds) / 2.0);
//    //  当前显示的区域
//    CGRect targetRect = CGRectMake(proposedContentOffset.x, 0.0, self.collectionView.bounds.size.width, self.collectionView.bounds.size.height);
//    //  取当前显示的item
//    NSArray* array = [super layoutAttributesForElementsInRect:targetRect];
//    //  对当前屏幕中的UICollectionViewLayoutAttributes逐个与屏幕中心进行比较，找出最接近中心的一个
//    for (UICollectionViewLayoutAttributes* layoutAttributes in array) {
//        CGFloat itemHorizontalCenter = layoutAttributes.center.x;
//        if (ABS(itemHorizontalCenter - horizontalCenter) < ABS(offsetAdjustment)) {
//            offsetAdjustment = itemHorizontalCenter - horizontalCenter;
//        }
//    }
//    return CGPointMake(proposedContentOffset.x, proposedContentOffset.y);
//}
//

@end
