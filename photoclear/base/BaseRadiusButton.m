//
//  BaseRadiusButton.m
//  photoclear
//
//  Created by lifubing on 2021/6/11.
//

#import "BaseRadiusButton.h"

@interface BaseRadiusButton () {
    CAShapeLayer *cornerRadiusLayer;
}

@end

@implementation BaseRadiusButton

- (void)awakeFromNib {
    [super awakeFromNib];
    if (self.cornerRadii == 0) {
        self.cornerRadii = self.frame.size.height / 2;
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    UIBezierPath *cornerRadiusPath = [UIBezierPath bezierPathWithRoundedRect:self.bounds byRoundingCorners:UIRectCornerAllCorners cornerRadii:CGSizeMake(self.cornerRadii, self.cornerRadii)];
    cornerRadiusLayer = [[CAShapeLayer alloc ] init];
    cornerRadiusLayer.frame = self.bounds;
    cornerRadiusLayer.path = cornerRadiusPath.CGPath;
    self.layer.mask = cornerRadiusLayer;
}

@end
