//
//  FQ_CollectionViewCell.h
//  test+collectionView
//
//  Created by fanqi on 2018/9/28.
//  Copyright © 2018年 fanqi. All rights reserved.
//

#import <UIKit/UIKit.h>
#define BUBBLE_DIAMETER     self.view.bounds.size.width
#define BUBBLE_PADDING      20.0
#define ScreenW  [UIScreen mainScreen].bounds.size.width
#define ScreenH  [UIScreen mainScreen].bounds.size.height

@protocol FQ_CollectionViewCellDelegate <NSObject>
- (void)singleTapd:(UIGestureRecognizer *)sender;
@end


@interface FQ_CollectionViewCell : UICollectionViewCell
@property (nonatomic, strong) UIImageView *contentImageView;
@property (nonatomic, weak) id<FQ_CollectionViewCellDelegate> delegate;

- (void)updateImage:(UIImage *)image;
@end

@interface FQ_CollectionViewFlowLayout : UICollectionViewFlowLayout

@end

@interface FQ_CustomCollectionViewLayoutAttributes : UICollectionViewLayoutAttributes

@property (nonatomic, assign) CGFloat progress;

@end

