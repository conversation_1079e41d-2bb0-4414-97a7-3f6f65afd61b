//
//  FQ_CollectionViewCell.m
//  test+collectionView
//
//  Created by fanqi on 2018/9/28.
//  Copyright © 2018年 fanqi. All rights reserved.
//

#import "FQ_CollectionViewCell.h"

@implementation FQ_CustomCollectionViewLayoutAttributes

-(id)copyWithZone:(NSZone *)zone
{
    FQ_CustomCollectionViewLayoutAttributes *attributes = [super copyWithZone:zone];
    attributes.progress = self.progress;
    return attributes;
}

@end


@interface FQ_CollectionViewFlowLayout()

@property (nonatomic, strong) NSMutableArray *attributesArray;

@end

@implementation FQ_CollectionViewFlowLayout

+(Class)layoutAttributesClass
{
    return [FQ_CustomCollectionViewLayoutAttributes class];
}

-(void)prepareLayout
{
    [super prepareLayout];
//    
//    [_attributesArray removeAllObjects];
//    
//    NSInteger cellCount = [self.collectionView numberOfItemsInSection:0];
//    //横向间距
//    //self.minimumLineSpacing
//    CGFloat indexX = 0.0f;
//    
//    for (int i = 0; i < cellCount ; ++i) {
//        FQ_CustomCollectionViewLayoutAttributes * layoutAttributes = [FQ_CustomCollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:[NSIndexPath indexPathForItem:i inSection:0]];
//
//        layoutAttributes.frame = CGRectMake(indexX, layoutAttributes.frame.origin.y, ScreenW, self.collectionView.bounds.size.height);
////
////        //明天调整这块.....
////        if (i == [self getSelectCurrentIndex]) {
////            layoutAttributes.progress = [self getScrollProgress];
////        }else if(i == ([self getSelectCurrentIndex] + 1) && [self getSelectCurrentIndex] != cellCount - 1){
////            layoutAttributes.progress = -(1 - [self getScrollProgress]);
////        }else{
//            
////        }
////
//        [self.attributesArray addObject:layoutAttributes];
////
//        indexX = indexX + (ScreenW + self.minimumLineSpacing);
//    }
//
//    //这次根据情况添加
//    [self.collectionView reloadData];
}

//2.提供布局属性对象
-(NSArray<FQ_CustomCollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
    return self.attributesArray.copy;
}


//1.提供滚动范围
-(CGSize)collectionViewContentSize
{
    return CGSizeMake((ScreenW + self.minimumLineSpacing) * self.attributesArray.count - self.minimumLineSpacing, self.collectionView.bounds.size.height);
}

-(BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds
{
    return YES;
}

-(int)getSelectCurrentIndex
{
    int selectIndex = self.collectionView.contentOffset.x / (ScreenW + self.minimumLineSpacing);
    return selectIndex;
}

-(CGFloat)getScrollProgress{
    CGFloat progress = self.collectionView.contentOffset.x / (ScreenW + self.minimumLineSpacing) - [self getSelectCurrentIndex];
    return MAX(MIN(progress, 1), 0) ;
}

-(NSMutableArray *)attributesArray
{
    if (!_attributesArray) {
        _attributesArray = [NSMutableArray array];
    }
    return _attributesArray;
}

@end


@interface FQ_CollectionViewCell() <UIScrollViewDelegate, UIGestureRecognizerDelegate> {
    BOOL isTwiceTaping;
}
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UITapGestureRecognizer *singleTapRecognizer;
@property (nonatomic, strong) UITapGestureRecognizer *doubleTapRecognizer;
@property (nonatomic, strong) UIView *doubleTapRecognizerView;
@end

@implementation FQ_CollectionViewCell

-(instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self scrollView];
        [self.scrollView addSubview:self.contentImageView];
        
        self.singleTapRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(singleTapd:)];
        [self.scrollView addGestureRecognizer:self.singleTapRecognizer];
        
        self.doubleTapRecognizerView = [[UIView alloc] initWithFrame:CGRectMake(frame.size.width/3, 0, frame.size.width/3, frame.size.height)];
        self.doubleTapRecognizerView.backgroundColor = [UIColor clearColor];
        [self.scrollView addSubview:self.doubleTapRecognizerView];
        
        self.doubleTapRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(doubleTaped:)];
        self.doubleTapRecognizer.numberOfTapsRequired = 2;
        [self.doubleTapRecognizerView addGestureRecognizer:self.doubleTapRecognizer];
    }
    return self;
}

//- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
//    CGPoint point = [gestureRecognizer locationInView:self];
//    if (gestureRecognizer == self.singleTapRecognizer) {
//        if (point.x < self.frame.size.width/4) {
//            return YES;
//        } else if (point.x > self.frame.size.width/4 * 3) {
//            return YES;
//        }
//        return NO;
//    } else {
//        if (point.x < self.frame.size.width/4) {
//            return NO;
//        } else if (point.x > self.frame.size.width/4 * 3) {
//            return NO;
//        }
//        return YES;
//    }
//}


- (void)singleTapd:(UIGestureRecognizer *)sender {
    NSLog(@"singleTapd");
    if (self.delegate) {
        [self.delegate singleTapd:sender];
    }
}

- (void)doubleTaped:(UITapGestureRecognizer *)sender {
    if (isTwiceTaping) {
        return;
    }
    isTwiceTaping = YES;
    CGFloat newScale;
    if (self.scrollView.zoomScale > 1) {
        newScale = 1.0;
    } else {
        newScale = 6.0;
    }
    [_scrollView setZoomScale:newScale animated:YES];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.35 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self->isTwiceTaping = NO;
    });
}

- (void)prepareForReuse{
    [super prepareForReuse];
    
    [self.scrollView setZoomScale:1.0];
    
}

- (void)updateImage:(UIImage *)image {
    self.contentImageView.image = image;
    
    CGSize maxSize = self.scrollView.frame.size;
    CGFloat widthRatio = maxSize.width/image.size.width;
    CGFloat heightRatio = maxSize.height/image.size.height;
    CGFloat initialZoom = (widthRatio > heightRatio) ? heightRatio : widthRatio;
    
    if (initialZoom > 1) {
        initialZoom = 1;
    }
    
    CGRect r = self.scrollView.frame;
    r.size.width = image.size.width * initialZoom;
    r.size.height = image.size.height * initialZoom;
    self.contentImageView.frame = r;
    self.contentImageView.center = CGPointMake(self.scrollView.frame.size.width/2, self.scrollView.frame.size.height/2);
    self.contentImageView.image = image;
    
    [self.scrollView setMinimumZoomScale:initialZoom];
    [self.scrollView setMaximumZoomScale:5];
    [self.scrollView setZoomScale:1.0];
}


-(UIImageView *)contentImageView {
    if (!_contentImageView) {
        _contentImageView = [[UIImageView alloc]init];
        _contentImageView.backgroundColor = [UIColor blackColor];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _contentImageView;
}

- (UIView *)viewForZoomingInScrollView:(UIScrollView *)scrollView {
    return self.contentImageView;
}

- (void)scrollViewDidZoom:(UIScrollView *)scrollView {
    CGFloat offsetX = (scrollView.bounds.size.width > scrollView.contentSize.width)?
    (scrollView.bounds.size.width - scrollView.contentSize.width) * 0.5 : 0.0;
    
    CGFloat offsetY = (scrollView.bounds.size.height > scrollView.contentSize.height)?
    (scrollView.bounds.size.height - scrollView.contentSize.height) * 0.5 : 0.0;
    
    self.contentImageView.center = CGPointMake(scrollView.contentSize.width * 0.5 + offsetX,
                                        scrollView.contentSize.height * 0.5 + offsetY);
    self.doubleTapRecognizerView.center = [scrollView convertPoint:scrollView.center fromView:self];
    
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    self.doubleTapRecognizerView.center = [scrollView convertPoint:scrollView.center fromView:self];
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(10, 0, self.frame.size.width-20, self.frame.size.height)];
        _scrollView.delegate = self;
        _scrollView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleWidth;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.showsVerticalScrollIndicator = NO;
        [self addSubview:_scrollView];
    }
    return _scrollView;
}


@end
