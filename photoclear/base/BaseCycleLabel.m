//
//  BaseCycleLabel.m
//  photoclear
//
//  Created by lifubing on 2021/6/11.
//

#import "BaseCycleLabel.h"
#import "UIView+Extension.h"

@interface BaseCycleLabel () {
    CAShapeLayer *cornerRadiusLayer;
}

@end

@implementation BaseCycleLabel

- (void)awakeFromNib {
    [super awakeFromNib];
    [self sizeToFit];
    if (self.height < 22) {
        self.height = 22;
    }
    if (self.width < self.height) {
        self.width = self.height;
    }
    if (self.cornerRadii == 0) {
        self.cornerRadii = self.height/2;
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    UIBezierPath *cornerRadiusPath = [UIBezierPath bezierPathWithRoundedRect:self.bounds byRoundingCorners:UIRectCornerAllCorners cornerRadii:CGSizeMake(self.cornerRadii, self.cornerRadii)];
    cornerRadiusLayer = [[CAShapeLayer alloc ] init];
    cornerRadiusLayer.frame = self.bounds;
    cornerRadiusLayer.path = cornerRadiusPath.CGPath;
    self.layer.mask = cornerRadiusLayer;
}

@end
