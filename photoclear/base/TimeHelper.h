//
//  TimeHelper.h
//  StepOfMyWorld
//
//  Created by <PERSON>,Fubing on 2018/4/30.
//  Copyright © 2018年 Li,Fubing. All rights reserved.
//

#import <Foundation/Foundation.h>

#define TimeHelperSingleton [TimeHelper shareInstance]

@interface TimeHelper : NSObject
+ (TimeHelper *)shareInstance;

- (NSInteger)getHourByIntervalTime:(NSInteger)Time;
- (NSInteger)getMineOfDayByIntervalTime:(NSInteger)Time;
- (NSInteger)getSecondOfDayByIntervalTime:(NSInteger)Time;
- (NSInteger)getDayByIntervalTime:(NSInteger)Time;

- (NSInteger)getDayOfThisYearByIntervalTime:(NSInteger)Time;

- (NSInteger)getYearByIntervalTime:(NSInteger)Time;
- (NSInteger)getMonthByIntervalTime:(NSInteger)Time;
- (NSString *)getDateByIntervalTime:(NSInteger)Time withformate:(NSString *)format;

- (NSDate *)getZeroTimeWithTime:(NSInteger)time;
- (NSInteger)getZeroDayIntervalTimeWithTime:(NSInteger)time;
- (NSDate *)getZeroTime;
- (NSDate *)getYearZeroTime;
- (NSDate *)getZeroWeekTime;

- (NSInteger)getIntervalTimeWithStr:(NSString *)str;

- (NSDate *)getDateWithYear:(NSInteger)year month:(NSInteger)month day:(NSInteger)day;
@end
