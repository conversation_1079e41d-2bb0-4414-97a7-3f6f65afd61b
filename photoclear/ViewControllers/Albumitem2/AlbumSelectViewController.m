//
//  AlbumSelectViewController.m
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "AlbumSelectViewController.h"
#import <Photos/Photos.h>
#import "PhotoSelectViewController.h"
#import "AlbumTableViewCell.h"

@interface AlbumSelectViewController () <UITableViewDelegate, UITableViewDataSource, PhotoSelectViewControllerDelegate>
@property (weak, nonatomic) IBOutlet UITableView *tableView;
@property (nonatomic, strong) PHFetchResult<PHAssetCollection *> *smartAlbumsArray;
@property (nonatomic, strong) PHFetchResult<PHAsset *> *photoAssetArray;
@property (nonatomic, strong) PHImageRequestOptions *requestOptions;

@end

@implementation AlbumSelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [_tableView registerClass:[AlbumTableViewCell class] forCellReuseIdentifier:@"AlbumTableViewCell"];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        // 所有智能相册
        PHFetchResult *smartAlbums = [PHAssetCollection fetchAssetCollectionsWithType:PHAssetCollectionTypeAlbum subtype:PHAssetCollectionSubtypeAny options:nil];
        self.smartAlbumsArray = smartAlbums;
        self.photoAssetArray = [self allPhotos];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.tableView reloadData];
        });
    });
}

- (PHFetchResult<PHAsset *> *)allPhotos {
    self.requestOptions = [[PHImageRequestOptions alloc] init];//请求选项设置
    self.requestOptions.resizeMode = PHImageRequestOptionsResizeModeExact;//自定义图片大小的加载模式
    self.requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    self.requestOptions.synchronous = YES;//是否同步加载
    
    PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
    fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
    //容器类
    return [PHAsset fetchAssetsWithMediaType:PHAssetMediaTypeImage options:fetchOptions]; //得到所有图片
}

#pragma mark - <  UITableViewDelegate >

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.smartAlbumsArray.count+1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    AlbumTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"AlbumTableViewCell" forIndexPath:indexPath];
    
    if (indexPath.row == 0) {
        NSString *title = [NSString stringWithFormat:@"最近项目"];
        PHAsset *phAsset = self.photoAssetArray.count > 0 ? [self.photoAssetArray objectAtIndex:0] : nil;
        [cell configureWithTitle:title asset:phAsset count:self.photoAssetArray.count requestOptions:self.requestOptions];
    } else {
        PHAssetCollection *collection = [self.smartAlbumsArray objectAtIndex:indexPath.row-1];
        
        PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
        fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
        PHFetchResult *fetchResult = [PHAsset fetchAssetsInAssetCollection:collection options:fetchOptions];
        
        PHAsset *phAsset = fetchResult.firstObject;
        // 只显示有照片的相册
        if (fetchResult.count > 0) {
            [cell configureWithTitle:collection.localizedTitle asset:phAsset count:fetchResult.count requestOptions:self.requestOptions];
        } else {
            [cell configureWithTitle:collection.localizedTitle asset:nil count:0 requestOptions:self.requestOptions];
        }
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (indexPath.row == 0) {
        [self PhotoSelectViewControllerDidSelect:nil andPhotoAsset:self.photoAssetArray andIndex:0];
    } else {
        PHAssetCollection *collection = [self.smartAlbumsArray objectAtIndex:indexPath.row-1];
        PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
        fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
        //容器类
        PHFetchResult *fetchResult = [PHAsset fetchAssetsInAssetCollection:collection options:fetchOptions];
        [self PhotoSelectViewControllerDidSelect:collection andPhotoAsset:fetchResult andIndex:0];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 80;
}

- (void)fetchImageWithIndex:(NSInteger)imageIndex withCompletionHandler:(void (^)(PHAsset * phAsset, UIImage *image))completionHandler {
    if (imageIndex >= self.photoAssetArray.count || imageIndex < 0) {
        if (completionHandler) {
            completionHandler(nil, nil);
        }
        return;
    }
    
    PHAsset *phAsset = [self.photoAssetArray objectAtIndex:imageIndex];
    // 使用统一的缩略图尺寸
    CGSize thumbnailSize = CGSizeMake(80, 60);
    [[PHImageManager defaultManager] requestImageForAsset:phAsset targetSize:thumbnailSize contentMode:PHImageContentModeAspectFill options:self.requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        if (completionHandler) {
            completionHandler(phAsset, result);
        }
    }];
}

- (void)PhotoSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index {
    if (self.delegate) {
        // 如果有代理，使用代理模式（从 PhotoSelectViewController 打开的情况）
        [self.delegate AlbumSelectViewControllerDidSelect:collection andPhotoAsset:photoAssetArray andIndex:index];
        [self dismissViewControllerAnimated:YES completion:nil];
    } else {
        // 如果没有代理，直接跳转到 PhotoSelectViewController（从主页打开的情况）
        UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
        PhotoSelectViewController *photoSelectViewController = [storyBoard instantiateViewControllerWithIdentifier:@"PhotoSelectViewController"];
        
        // 设置相册数据
        photoSelectViewController.fetchResult = photoAssetArray;
        photoSelectViewController.collection = collection;
        
        [self.navigationController pushViewController:photoSelectViewController animated:YES];
    }
}

@end
