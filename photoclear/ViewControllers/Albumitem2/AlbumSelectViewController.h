//
//  AlbumSelectViewController.h
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "BaseViewController.h"
#import <Photos/Photos.h>

NS_ASSUME_NONNULL_BEGIN

@protocol AlbumSelectViewControllerDelegate <NSObject>
- (void)AlbumSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index;
@end

@interface AlbumSelectViewController : BaseViewController
@property (nonatomic, weak) id<AlbumSelectViewControllerDelegate> delegate;
@end

NS_ASSUME_NONNULL_END
