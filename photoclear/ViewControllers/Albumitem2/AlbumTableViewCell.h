//
//  AlbumTableViewCell.h
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/22.
//

#import <UIKit/UIKit.h>
#import <Photos/Photos.h>

NS_ASSUME_NONNULL_BEGIN

@interface AlbumTableViewCell : UITableViewCell

@property (nonatomic, strong) UIImageView *albumImageView;
@property (nonatomic, strong) UILabel *albumTitleLabel;
@property (nonatomic, strong) UILabel *countLabel;

- (void)configureWithTitle:(NSString *)title asset:(PHAsset * _Nullable)asset requestOptions:(PHImageRequestOptions *)requestOptions;
- (void)configureWithTitle:(NSString *)title asset:(PHAsset * _Nullable)asset count:(NSInteger)count requestOptions:(PHImageRequestOptions *)requestOptions;

@end

NS_ASSUME_NONNULL_END