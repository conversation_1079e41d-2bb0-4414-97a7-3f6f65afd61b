//
//  AlbumTableViewCell.m
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/22.
//

#import "AlbumTableViewCell.h"

@implementation AlbumTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor blackColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 创建图片视图
    self.albumImageView = [[UIImageView alloc] init];
    self.albumImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.albumImageView.clipsToBounds = YES;
    self.albumImageView.layer.cornerRadius = 8.0;
    self.albumImageView.backgroundColor = [UIColor darkGrayColor];
    self.albumImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.albumImageView];
    
    // 创建标题标签
    self.albumTitleLabel = [[UILabel alloc] init];
    self.albumTitleLabel.textColor = [UIColor lightGrayColor];
    self.albumTitleLabel.font = [UIFont systemFontOfSize:16];
    self.albumTitleLabel.numberOfLines = 1;
    self.albumTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.albumTitleLabel];
    
    // 创建数量标签 - 显示在图片右下角
    self.countLabel = [[UILabel alloc] init];
    self.countLabel.textColor = [UIColor whiteColor];
    self.countLabel.font = [UIFont boldSystemFontOfSize:12];
    self.countLabel.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
    self.countLabel.textAlignment = NSTextAlignmentCenter;
    self.countLabel.layer.cornerRadius = 8.0;
    self.countLabel.clipsToBounds = YES;
    self.countLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.albumImageView addSubview:self.countLabel];
    
    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 图片视图约束 - 固定尺寸 80x60，左边距16，垂直居中
        [self.albumImageView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:16],
        [self.albumImageView.centerYAnchor constraintEqualToAnchor:self.contentView.centerYAnchor],
        [self.albumImageView.widthAnchor constraintEqualToConstant:80],
        [self.albumImageView.heightAnchor constraintEqualToConstant:60],
        
        // 标题标签约束 - 图片右边16间距，垂直居中，右边距16
        [self.albumTitleLabel.leadingAnchor constraintEqualToAnchor:self.albumImageView.trailingAnchor constant:16],
        [self.albumTitleLabel.centerYAnchor constraintEqualToAnchor:self.contentView.centerYAnchor],
        [self.albumTitleLabel.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-16],
        
        // 数量标签约束 - 位于图片右下角
        [self.countLabel.trailingAnchor constraintEqualToAnchor:self.albumImageView.trailingAnchor constant:-4],
        [self.countLabel.bottomAnchor constraintEqualToAnchor:self.albumImageView.bottomAnchor constant:-4],
        [self.countLabel.widthAnchor constraintGreaterThanOrEqualToConstant:22],
        [self.countLabel.heightAnchor constraintEqualToConstant:16]
    ]];
}

- (void)configureWithTitle:(NSString *)title asset:(PHAsset *)asset requestOptions:(PHImageRequestOptions *)requestOptions {
    [self configureWithTitle:title asset:asset count:0 requestOptions:requestOptions];
}

- (void)configureWithTitle:(NSString *)title asset:(PHAsset *)asset count:(NSInteger)count requestOptions:(PHImageRequestOptions *)requestOptions {
    self.albumTitleLabel.text = title;
    self.countLabel.text = [NSString stringWithFormat:@"%ld", count];
    [self.countLabel sizeToFit];
    CGFloat width = self.countLabel.frame.size.width;
    
    self.countLabel.layer.cornerRadius = 8.0;
    self.countLabel.clipsToBounds = YES;
    self.countLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        [self.countLabel.widthAnchor constraintGreaterThanOrEqualToConstant:width + 4],
    ]];
    
    if (asset) {
        // 使用固定的缩略图尺寸
        CGSize thumbnailSize = CGSizeMake(80, 60);
        [[PHImageManager defaultManager] requestImageForAsset:asset 
                                                   targetSize:thumbnailSize 
                                                  contentMode:PHImageContentModeAspectFill 
                                                      options:requestOptions 
                                                resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.albumImageView.image = result;
            });
        }];
    } else {
        self.albumImageView.image = [UIImage imageNamed:@"default_pic"];
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    self.albumImageView.image = nil;
    self.albumTitleLabel.text = nil;
    self.countLabel.text = nil;
}

@end
