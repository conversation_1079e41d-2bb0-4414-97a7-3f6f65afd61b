//
//  DeletePhotoCollectionViewCell.h
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/18.
//

#import <UIKit/UIKit.h>
#import <Photos/Photos.h>

NS_ASSUME_NONNULL_BEGIN

@protocol DeletePhotoCollectionViewCellDelegate <NSObject>
- (void)deletePhotoCell:(UICollectionViewCell *)cell didTapRemoveButtonAtIndex:(NSInteger)index;
@end

@interface DeletePhotoCollectionViewCell : UICollectionViewCell

@property (nonatomic, weak) id<DeletePhotoCollectionViewCellDelegate> delegate;
@property (nonatomic, assign) NSInteger cellIndex;

- (void)configureWithAsset:(PHAsset *)asset;

@end

NS_ASSUME_NONNULL_END