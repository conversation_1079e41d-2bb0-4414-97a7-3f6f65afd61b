//
//  WaterfallFlowLayout.h
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/18.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol WaterfallFlowLayoutDelegate <NSObject>

@required
- (CGFloat)waterfallFlowLayout:(UICollectionViewLayout *)layout heightForItemAtIndexPath:(NSIndexPath *)indexPath itemWidth:(CGFloat)itemWidth;

@optional
- (NSInteger)numberOfColumnsInWaterfallFlowLayout:(UICollectionViewLayout *)layout;
- (CGFloat)columnSpacingInWaterfallFlowLayout:(UICollectionViewLayout *)layout;
- (CGFloat)rowSpacingInWaterfallFlowLayout:(UICollectionViewLayout *)layout;
- (UIEdgeInsets)edgeInsetsInWaterfallFlowLayout:(UICollectionViewLayout *)layout;

@end

@interface WaterfallFlowLayout : UICollectionViewLayout

@property (nonatomic, weak) id<WaterfallFlowLayoutDelegate> delegate;

@end

NS_ASSUME_NONNULL_END