//
//  WaterfallFlowLayout.m
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/18.
//

#import "WaterfallFlowLayout.h"

@interface WaterfallFlowLayout ()

@property (nonatomic, strong) NSMutableArray<UICollectionViewLayoutAttributes *> *attributesArray;
@property (nonatomic, strong) NSMutableArray<NSNumber *> *columnHeights;
@property (nonatomic, assign) CGFloat contentHeight;

@end

@implementation WaterfallFlowLayout

#pragma mark - Lifecycle

- (void)prepareLayout {
    [super prepareLayout];
    
    self.contentHeight = 0;
    
    if (!self.attributesArray) {
        self.attributesArray = [NSMutableArray array];
    } else {
        [self.attributesArray removeAllObjects];
    }
    
    if (!self.columnHeights) {
        self.columnHeights = [NSMutableArray array];
    } else {
        [self.columnHeights removeAllObjects];
    }
    
    // 初始化列高度数组
    NSInteger numberOfColumns = [self numberOfColumns];
    for (NSInteger i = 0; i < numberOfColumns; i++) {
        [self.columnHeights addObject:@([self edgeInsets].top)];
    }
    
    // 计算每个item的布局属性
    NSInteger numberOfItems = [self.collectionView numberOfItemsInSection:0];
    for (NSInteger i = 0; i < numberOfItems; i++) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForItem:i inSection:0];
        UICollectionViewLayoutAttributes *attributes = [self layoutAttributesForItemAtIndexPath:indexPath];
        [self.attributesArray addObject:attributes];
    }
}

- (CGSize)collectionViewContentSize {
    CGFloat maxHeight = 0;
    for (NSNumber *height in self.columnHeights) {
        if (height.floatValue > maxHeight) {
            maxHeight = height.floatValue;
        }
    }
    
    return CGSizeMake(self.collectionView.bounds.size.width, maxHeight + [self edgeInsets].bottom);
}

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    NSMutableArray *attributesInRect = [NSMutableArray array];
    
    for (UICollectionViewLayoutAttributes *attributes in self.attributesArray) {
        if (CGRectIntersectsRect(attributes.frame, rect)) {
            [attributesInRect addObject:attributes];
        }
    }
    
    return attributesInRect;
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    UICollectionViewLayoutAttributes *attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
    
    CGFloat collectionViewWidth = self.collectionView.bounds.size.width;
    UIEdgeInsets edgeInsets = [self edgeInsets];
    CGFloat columnSpacing = [self columnSpacing];
    NSInteger numberOfColumns = [self numberOfColumns];
    
    // 计算item宽度
    CGFloat availableWidth = collectionViewWidth - edgeInsets.left - edgeInsets.right - (numberOfColumns - 1) * columnSpacing;
    CGFloat itemWidth = availableWidth / numberOfColumns;
    
    // 获取item高度
    CGFloat itemHeight = [self.delegate waterfallFlowLayout:self heightForItemAtIndexPath:indexPath itemWidth:itemWidth];
    
    // 找到最短的列
    NSInteger shortestColumnIndex = 0;
    CGFloat shortestHeight = [self.columnHeights[0] floatValue];
    
    for (NSInteger i = 1; i < numberOfColumns; i++) {
        CGFloat height = [self.columnHeights[i] floatValue];
        if (height < shortestHeight) {
            shortestHeight = height;
            shortestColumnIndex = i;
        }
    }
    
    // 计算item的位置
    CGFloat itemX = edgeInsets.left + shortestColumnIndex * (itemWidth + columnSpacing);
    CGFloat itemY = shortestHeight;
    
    // 如果不是第一行，需要加上行间距
    if (itemY > edgeInsets.top) {
        itemY += [self rowSpacing];
    }
    
    attributes.frame = CGRectMake(itemX, itemY, itemWidth, itemHeight);
    
    // 更新列高度
    self.columnHeights[shortestColumnIndex] = @(itemY + itemHeight);
    
    return attributes;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds {
    return !CGSizeEqualToSize(newBounds.size, self.collectionView.bounds.size);
}

#pragma mark - Private Methods

- (NSInteger)numberOfColumns {
    if (self.delegate && [self.delegate respondsToSelector:@selector(numberOfColumnsInWaterfallFlowLayout:)]) {
        return [self.delegate numberOfColumnsInWaterfallFlowLayout:self];
    }
    return 2; // 默认2列
}

- (CGFloat)columnSpacing {
    if (self.delegate && [self.delegate respondsToSelector:@selector(columnSpacingInWaterfallFlowLayout:)]) {
        return [self.delegate columnSpacingInWaterfallFlowLayout:self];
    }
    return 10; // 默认列间距
}

- (CGFloat)rowSpacing {
    if (self.delegate && [self.delegate respondsToSelector:@selector(rowSpacingInWaterfallFlowLayout:)]) {
        return [self.delegate rowSpacingInWaterfallFlowLayout:self];
    }
    return 10; // 默认行间距
}

- (UIEdgeInsets)edgeInsets {
    if (self.delegate && [self.delegate respondsToSelector:@selector(edgeInsetsInWaterfallFlowLayout:)]) {
        return [self.delegate edgeInsetsInWaterfallFlowLayout:self];
    }
    return UIEdgeInsetsMake(10, 10, 10, 10); // 默认边距
}

@end