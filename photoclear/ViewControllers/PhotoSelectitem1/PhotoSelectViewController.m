//
//  PhotoSelectViewController.m
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "PhotoSelectViewController.h"
#import "PhotoCollectionViewCell.h"
#import "UIButton+CenterImageAndTitle.h"
#import "ScrollViewController.h"
#import "PhotoTools.h"
#import "AlbumSelectViewController.h"
#import "Preferences.h"

@interface PhotoSelectViewController () <UICollectionViewDelegate, UICollectionViewDataSource, AlbumSelectViewControllerDelegate>
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIButton *titleBtn;

@property (nonatomic, strong) PHImageRequestOptions *requestOptions;
@property (nonatomic, assign) CGFloat cellwidth;
@property (nonatomic, assign) CGFloat cellMargin;
@property (nonatomic, strong) UIButton *backButton;
@end

@implementation PhotoSelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    if (self.fetchResult) {
        [self setupBackButton];
    } else {
        // 如果没有传入 fetchResult，使用默认的所有照片
        self.fetchResult = [PhotoTools allPhotos];
    }
    
    // 设置标题
    [self updateTitleButton];
    
    
    self.cellMargin = 2;
    self.cellwidth = floorf((self.view.frame.size.width - self.cellMargin * 2 - 4 * 3) / 4);
    
    [_collectionView registerClass:[PhotoCollectionViewCell class] forCellWithReuseIdentifier:@"PhotoCollectionViewCell"];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
        
    self.requestOptions = [[PHImageRequestOptions alloc] init];//请求选项设置
    self.requestOptions.resizeMode = PHImageRequestOptionsResizeModeExact;//自定义图片大小的加载模式
    self.requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    self.requestOptions.synchronous = YES;//是否同步加载
}

- (void)fetchImageWithIndex:(NSInteger)imageIndex withCompletionHandler:(void (^)(PHAsset * phAsset, UIImage *image))completionHandler {
    if (imageIndex < 0 || imageIndex >= self.fetchResult.count) {
        return;
    }
    PHAsset *phAsset = [self.fetchResult objectAtIndex:imageIndex];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [[PHImageManager defaultManager] requestImageForAsset:phAsset targetSize:CGSizeMake(self.cellwidth * 1.5, self.cellwidth * 1.5) contentMode:PHImageContentModeAspectFill options:self.requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
            
            dispatch_async(dispatch_get_main_queue(), ^{ 
                if (completionHandler) {
                    completionHandler(phAsset, result);
                }
            });
        }];
    });
}

#pragma mark - <  UICollectionViewDelegate >

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    PhotoCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PhotoCollectionViewCell" forIndexPath:indexPath];
//    PHAsset *phAsset = [self.fetchResult objectAtIndex:indexPath.row];
    
//    if ([[Preferences sharedInstance].deleteArray indexOfObject:phAsset.localIdentifier] != NSNotFound) {
//        [cell setIsDelete:YES];
//    } else {
//        [cell setIsDelete:NO];
//    }
    
    [self fetchImageWithIndex:indexPath.row withCompletionHandler:^(PHAsset *phAsset, UIImage *image) {
        [cell updateImage:image];
    }];
    
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    CGSize cellSize = CGSizeMake(self.cellwidth, self.cellwidth);
    return cellSize;
}


// 两列cell之间的间距
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return self.cellMargin;
}


- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return self.cellMargin;
}


- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.fetchResult.count;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    ScrollViewController *photoScrollViewController = [storyBoard instantiateViewControllerWithIdentifier:@"ScrollViewController"];
    photoScrollViewController.photoAssetArray = self.fetchResult;
    photoScrollViewController.startIndexPath = indexPath;

    [self.navigationController pushViewController:photoScrollViewController animated:YES];
}

- (IBAction)titleBtnClicked:(UIButton *)sender {
    UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    AlbumSelectViewController *albumSelectViewController = [storyBoard instantiateViewControllerWithIdentifier:@"AlbumSelectViewController"];
    albumSelectViewController.title = @"选择相簿";
//    AlbumSelectViewController *albumSelectViewController = albumselectnavigation.viewControllers.firstObject;
    albumSelectViewController.delegate = self;
    
    [self presentViewController:albumSelectViewController animated:YES completion:nil];
}


- (void)updateTitleButton {
    NSString *titleText;
    NSInteger count = self.fetchResult.count;
    
    if (self.collection == nil) {
        titleText = [NSString stringWithFormat:@"%@（%ld）", @"最近项目", count];
    } else {
        titleText = [NSString stringWithFormat:@"%@（%ld）", self.collection.localizedTitle, count];
    }
    
    [self.titleBtn setTitle:titleText forState:UIControlStateNormal];
    [self.titleBtn setImage:[UIImage imageNamed:@"change"] forState:UIControlStateNormal];
    [self.titleBtn sizeToFit];
    [self.titleBtn horizontalCenterTitleAndImage:0];
}

- (void)setupBackButton {
    // 创建返回按钮
    self.backButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.backButton setImage:[UIImage imageNamed:@"icon_back"] forState:UIControlStateNormal];
    [self.backButton addTarget:self action:@selector(backButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    
    // 设置按钮位置
    self.backButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.backButton];
    
    // 添加约束 - 左上角位置
    [NSLayoutConstraint activateConstraints:@[
        [self.backButton.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:16],
//        [self.backButton.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:12],
        [self.backButton.centerYAnchor constraintEqualToAnchor:self.titleBtn.centerYAnchor],
        [self.backButton.widthAnchor constraintEqualToConstant:44],
        [self.backButton.heightAnchor constraintEqualToConstant:32],
    ]];
}

- (void)backButtonTapped {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)AlbumSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index {
    // 更新当前的 collection 属性
    self.collection = collection;
    self.fetchResult = photoAssetArray;
    
    // 更新标题
    [self updateTitleButton];
    
    [self.collectionView reloadData];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForRow:index inSection:0] atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
    });
}
@end
