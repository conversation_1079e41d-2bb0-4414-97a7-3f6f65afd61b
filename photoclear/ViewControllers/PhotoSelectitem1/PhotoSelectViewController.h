//
//  PhotoSelectViewController.h
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "BaseViewController.h"
#import <Photos/Photos.h>

NS_ASSUME_NONNULL_BEGIN

@protocol PhotoSelectViewControllerDelegate <NSObject>
- (void)PhotoSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index;
@end


@interface PhotoSelectViewController : BaseViewController
@property (nonatomic, strong) PHFetchResult *fetchResult;
@property (nonatomic, strong) PHAssetCollection *collection;
@property (nonatomic, weak) id<PhotoSelectViewControllerDelegate> delegate;
@end

NS_ASSUME_NONNULL_END
