//
//  PhotoCollectionViewCell.m
//  photoclear
//
//  Created by lifubing on 2021/6/13.
//

#import "PhotoCollectionViewCell.h"
#import "FQ_CollectionViewCell.h"

@implementation PhotoCollectionViewLayoutAttributes


-(id)copyWithZone:(NSZone *)zone
{
    PhotoCollectionViewLayoutAttributes *attributes = [super copyWithZone:zone];
    return attributes;
}

@end


@interface PhotoCollectionViewCellFlowLayout()

@property (nonatomic, strong) NSMutableArray *attributesArray;

@end

@implementation PhotoCollectionViewCellFlowLayout

+(Class)layoutAttributesClass
{
    return [PhotoCollectionViewLayoutAttributes class];
}

-(void)prepareLayout
{
    [super prepareLayout];
}

//2.提供布局属性对象
-(NSArray<PhotoCollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
    return self.attributesArray.copy;
}


//1.提供滚动范围
-(CGSize)collectionViewContentSize
{
    return CGSizeMake((ScreenW + self.minimumLineSpacing) * self.attributesArray.count - self.minimumLineSpacing, self.collectionView.bounds.size.height);
}

-(BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds
{
    return YES;
}

-(int)getSelectCurrentIndex
{
    int selectIndex = self.collectionView.contentOffset.x / (ScreenW + self.minimumLineSpacing);
    return selectIndex;
}

-(CGFloat)getScrollProgress{
    CGFloat progress = self.collectionView.contentOffset.x / (ScreenW + self.minimumLineSpacing) - [self getSelectCurrentIndex];
    return MAX(MIN(progress, 1), 0) ;
}

-(NSMutableArray *)attributesArray
{
    if (!_attributesArray) {
        _attributesArray = [NSMutableArray array];
    }
    return _attributesArray;
}

@end


@interface PhotoCollectionViewCell()
@property (nonatomic, strong) UIImageView *contentImageView;
@property (nonatomic, strong) UIView *coverView;
@end

@implementation PhotoCollectionViewCell

-(instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self addSubview:self.contentImageView];
        self.contentImageView.frame = self.contentView.bounds;
//        self.contentImageView.layer.cornerRadius = 2;
//        self.contentImageView.layer.masksToBounds = YES;
//        self.coverView = [[UIView alloc] initWithFrame:self.contentView.bounds];
//        self.coverView.backgroundColor = [[UIColor redColor] colorWithAlphaComponent:0.2];
//        self.coverView.hidden = YES;
//        [self addSubview:self.coverView];
    }
    return self;
}

- (void)updateImage:(UIImage *)image {
    self.contentImageView.image = image;
}

- (void)setIsDelete:(BOOL)isDelete {
    if (isDelete) {
        self.coverView.hidden = NO;
    } else {
        self.coverView.hidden = YES;
    }
}

//-(UICollectionViewLayoutAttributes *)preferredLayoutAttributesFittingAttributes:(FQ_CustomCollectionViewLayoutAttributes *)layoutAttributes
//{
//    [super preferredLayoutAttributesFittingAttributes:layoutAttributes];
////    self.contentImageView.transform = CGAffineTransformMakeTranslation(layoutAttributes.progress * BUBBLE_PADDING, 0);
//    return layoutAttributes;
//}


-(UIImageView *)contentImageView {
    if (!_contentImageView) {
        _contentImageView = [[UIImageView alloc]init];
        _contentImageView.backgroundColor = [UIColor blackColor];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _contentImageView;
}

@end

