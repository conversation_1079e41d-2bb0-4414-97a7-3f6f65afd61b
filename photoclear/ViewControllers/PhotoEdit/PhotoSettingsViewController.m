//
//  PhotoSettingsViewController.m
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/22.
//

#import "PhotoSettingsViewController.h"
#import "Preferences.h"

@interface PhotoSettingsViewController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) NSArray *settingsData;

@end

@implementation PhotoSettingsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self setupData];
    [self setupGestures];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor clearColor];
    
    // 背景遮罩
    self.backgroundView = [[UIView alloc] initWithFrame:self.view.bounds];
    self.backgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.backgroundView.alpha = 0;
    [self.view addSubview:self.backgroundView];
    
    // 设置表格 - 从上方出现
    CGFloat tableHeight = 360;
    // 获取安全区域顶部距离
    CGFloat safeAreaTop = 0;
//    if (@available(iOS 11.0, *)) {
//        safeAreaTop = self.view.safeAreaInsets.top;
//    }
//    if (safeAreaTop == 0) {
//        safeAreaTop = 44; // 默认状态栏高度
//    }
    
    // 初始位置在屏幕上方（隐藏）
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, -tableHeight, self.view.bounds.size.width, tableHeight) style:UITableViewStyleGrouped];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.backgroundColor = [UIColor blackColor];
    self.tableView.layer.cornerRadius = 2;
    // 修改圆角位置为底部圆角
    self.tableView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    // 设置夜间模式分割线颜色
    self.tableView.separatorColor = [UIColor colorWithWhite:0.3 alpha:1.0];
    [self.view addSubview:self.tableView];
    
    // 动画显示 - 从上方滑下
    [UIView animateWithDuration:0.3 animations:^{
        self.backgroundView.alpha = 1;
        self.tableView.frame = CGRectMake(0, safeAreaTop, self.view.bounds.size.width, tableHeight);
    }];
}

- (void)setupData {
    self.settingsData = @[
        @{
            @"title": @"自动播放",
            @"type": @"switch",
            @"key": @"autoPlayEnabled"
        },
        @{
            @"title": @"自动播放速度",
            @"type": @"segment",
            @"key": @"autoPlayInterval",
            @"options": @[@"2秒", @"3秒", @"4秒", @"5秒"],
            @"values": @[@2, @3, @4, @5]
        },
        @{
            @"title": @"滑动切换方向",
            @"type": @"segment", 
            @"key": @"swipeDirection",
            @"options": @[@"左右滑动", @"上下滑动"],
            @"values": @[@0, @1]
        }
    ];
}

- (void)setupGestures {
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped)];
    [self.backgroundView addGestureRecognizer:tapGesture];
}

- (void)backgroundTapped {
    [self dismissSettings];
}

- (void)dismissSettings {
    CGFloat tableHeight = 300;
    [UIView animateWithDuration:0.3 animations:^{
        self.backgroundView.alpha = 0;
        // 向上滑出屏幕
        self.tableView.frame = CGRectMake(0, -tableHeight, self.view.bounds.size.width, tableHeight);
    } completion:^(BOOL finished) {
        [self dismissViewControllerAnimated:NO completion:nil];
    }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.settingsData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *settingItem = self.settingsData[indexPath.row];
    NSString *type = settingItem[@"type"];
    
    UITableViewCell *cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:nil];
    cell.textLabel.text = settingItem[@"title"];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 设置夜间模式样式
    cell.backgroundColor = [UIColor blackColor];
    cell.textLabel.textColor = [UIColor whiteColor];
    
    if ([type isEqualToString:@"switch"]) {
        UISwitch *switchControl = [[UISwitch alloc] init];
        BOOL isOn = [[[Preferences sharedInstance] valueForKey:settingItem[@"key"]] boolValue];
        switchControl.on = isOn;
        // 设置夜间模式样式
        switchControl.onTintColor = [UIColor colorWithRed:0.0 green:0.5 blue:1.0 alpha:1.0]; // 蓝色
        switchControl.thumbTintColor = [UIColor whiteColor];
        switchControl.backgroundColor = [UIColor colorWithWhite:0.3 alpha:1.0];
        switchControl.layer.cornerRadius = switchControl.frame.size.height / 2;
        switchControl.tag = indexPath.row;
        [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        cell.accessoryView = switchControl;
    } else if ([type isEqualToString:@"segment"]) {
        NSArray *options = settingItem[@"options"];
        NSArray *values = settingItem[@"values"];
        
        UISegmentedControl *segmentControl = [[UISegmentedControl alloc] initWithItems:options];
        
        // 设置当前选中项
        id currentValue = [[Preferences sharedInstance] valueForKey:settingItem[@"key"]];
        NSInteger selectedIndex = [values indexOfObject:currentValue];
        if (selectedIndex != NSNotFound) {
            segmentControl.selectedSegmentIndex = selectedIndex;
        }
        
        // 设置夜间模式样式
        if (@available(iOS 13.0, *)) {
            segmentControl.backgroundColor = [UIColor colorWithWhite:0.2 alpha:1.0];
            segmentControl.selectedSegmentTintColor = [UIColor colorWithWhite:0.4 alpha:1.0];
            [segmentControl setTitleTextAttributes:@{NSForegroundColorAttributeName: [UIColor whiteColor]} forState:UIControlStateNormal];
            [segmentControl setTitleTextAttributes:@{NSForegroundColorAttributeName: [UIColor whiteColor]} forState:UIControlStateSelected];
        } else {
            segmentControl.backgroundColor = [UIColor colorWithWhite:0.2 alpha:1.0];
            segmentControl.tintColor = [UIColor colorWithWhite:0.4 alpha:1.0];
            [segmentControl setTitleTextAttributes:@{NSForegroundColorAttributeName: [UIColor whiteColor]} forState:UIControlStateNormal];
        }
        
        segmentControl.tag = indexPath.row;
        [segmentControl addTarget:self action:@selector(segmentValueChanged:) forControlEvents:UIControlEventValueChanged];
        cell.accessoryView = segmentControl;
    }
    
    return cell;
}

//- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
//    return self.settingsData[section][@"title"];
//}

#pragma mark - Control Actions

- (void)switchValueChanged:(UISwitch *)sender {
    NSDictionary *settingItem = self.settingsData[sender.tag];
    NSString *key = settingItem[@"key"];
    
    [[Preferences sharedInstance] setValue:@(sender.isOn) forKey:key];
    
    if ([self.delegate respondsToSelector:@selector(settingsDidChange)]) {
        [self.delegate settingsDidChange];
    }
}

- (void)segmentValueChanged:(UISegmentedControl *)sender {
    NSDictionary *settingItem = self.settingsData[sender.tag];
    NSString *key = settingItem[@"key"];
    NSArray *values = settingItem[@"values"];
    
    if (sender.selectedSegmentIndex < values.count) {
        id value = values[sender.selectedSegmentIndex];
        [[Preferences sharedInstance] setValue:value forKey:key];
        
        if ([self.delegate respondsToSelector:@selector(settingsDidChange)]) {
            [self.delegate settingsDidChange];
        }
    }
}

@end
