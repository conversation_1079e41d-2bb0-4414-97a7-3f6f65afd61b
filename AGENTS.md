# Repository Guidelines

## Project Structure & Module Organization
`photoclear/` contains the Objective-C app, including view controllers, `PhotoTools` utilities, and shareable UI from `photoclear/base/` (reusable controls, layout helpers, `PAPreferences`). Widget-facing glue sits in `WidgetKitHelper.swift`. `MyWidgetExtension/` holds the Swift widget timeline (`MyWidgetExtension.swift`), shared persistence in `SharedPhotoManager.swift`, and companion assets. Entitlement files (`photoclear.entitlements`, `MyWidgetExtensionExtension.entitlements`) configure the `group.com.lfb.manager.photoclear.shared` container that both targets rely on. Keep image and color resources inside each target's `Assets.xcassets`.

## Build, Test, and Development Commands
Use `xcodebuild -scheme photoclear -configuration Debug -sdk iphonesimulator build` for a local simulator build, adding `-destination 'platform=iOS Simulator,name=iPhone 15'` when you need a specific runtime. Build the widget alone with `xcodebuild -scheme MyWidgetExtensionExtension build` to check for extension-only regressions. Tests are not yet committed; once you add an XCTest target, run it with `xcodebuild test -scheme photoclear -destination 'platform=iOS Simulator,name=iPhone 15'`. `open photoclear.xcodeproj` remains the quickest path for UI-driven debugging.

## Coding Style & Naming Conventions
Follow the existing 4-space indentation and Allman braces in Objective-C files; prefer categories for UIKit tweaks (see `UIButton+CenterImageAndTitle`). Name classes and files using PascalCase and suffixes like `...ViewController` or `...Manager`. Swift code in the widget target should stay within a single namespace per type and rely on camelCase members. Run Xcode's "Editor > Structure > Re-Indent" before committing to keep formatting consistent.

## Testing Guidelines
Adopt `XCTestCase` with method names such as `testCachesWidgetPhotosWhenAuthorized`. Co-locate widget tests with shared managers so you can exercise both the `SharedPhotoManager` logic and Objective-C bridges. Include lightweight UI tests only when flows change, and capture photo caching fixtures in the shared group directory using throwaway copies.

## Commit & Pull Request Guidelines
Recent commits use bracketed prefixes (for example `[v1.0.0][2]`); continue that pattern with a version tag and issue ID before the summary, keeping the final phrase short and action oriented. For pull requests, include the context of the change, simulator or device evidence (screenshots or logs), and links to the relevant issue. Call out any entitlement or App Group adjustments explicitly so reviewers can validate provisioning updates.

## Widget & App Group Notes
When touching shared storage, confirm `AppDelegate` and `SharedPhotoManager` agree on the App Group identifier. Clean up generated photo caches under `WidgetPhotos` when altering formats, and document migrations in the PR to guide QA resets.
